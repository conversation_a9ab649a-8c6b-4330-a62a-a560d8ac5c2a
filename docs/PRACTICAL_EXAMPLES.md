# 实战示例：基于当前项目的优化提示词

## 示例 1：完整的电影搜索 API 迁移

### 背景
基于您当前的 `test/_fixtures/MovieApi_Final_withConsul/` 项目，这是一个完整的迁移示例。

### 优化后的提示词

```markdown
我正在使用一个成熟的 Tibco BW 转 Spring Boot 的 CLI 工具迁移电影搜索 API。

**项目背景**：
- 原始项目：MovieApi_Final_withConsul
- 主要功能：通过 OMDB API 搜索电影信息
- 包含：BWP 文件、XSD Schemas、Swagger 规范

**执行自动化迁移**：
```bash
# 一键自动转换
node dist/cli.js auto test/_fixtures/ -p com.example.movies --port 8080
```

**验证要求**：
1. **代码生成验证**：
   - [ ] SearchMoviesController.java 生成
   - [ ] SearchMoviesService.java 生成  
   - [ ] 30+ XSD 模型类生成
   - [ ] application.properties 包含 OMDB API 配置

2. **功能验证**：
   ```bash
   # 启动应用
   cd spring-boilerplate && mvn spring-boot:run
   
   # 测试 API
   curl "http://localhost:8080/movies?searchString=batman"
   
   # 预期响应格式
   {"search":[{"title":"Batman","year":"1989",...}],"totalResults":"613","response":"True"}
   ```

3. **问题排查**：
   - 如果返回空 search 数组，检查 OMDB API 配置
   - 如果 API 调用失败，验证外部服务配置
   - 如果编译失败，检查依赖和包结构

**成功标准**：
- Spring Boot 应用正常启动
- API 返回正确的电影搜索结果
- 所有生成的测试通过
- 与原始 Swagger 规范一致

请确保整个迁移流程顺利完成，并提供详细的验证报告。
```

## 示例 2：针对特定问题的修复

### 基于您提到的 toString 和 API 调用问题

```markdown
我的 Tibco BW 迁移项目遇到了具体的技术问题，需要针对性修复。

**问题 1：toString 转换问题**
- 现象：生成的 Java 类 toString 方法有问题
- 影响：调试和日志输出异常

**问题 2：外部 API 调用错误**
- 现象：调用 OMDB API 时出现 "URI is not absolute" 错误
- 当前错误：`java.lang.IllegalArgumentException: URI is not absolute`

**问题 3：API 响应数据不完整**
- 现象：返回 `{"search":[],"totalResults":"613","response":null}`
- 预期：search 数组应包含电影数据

**修复策略**：

1. **分析原始配置**：
   ```bash
   # 检查原始 BWP 文件中的外部服务配置
   grep -r "omdbapi" test/_fixtures/
   
   # 查看 substvar 文件中的 API 配置
   find test/_fixtures/ -name "*.substvar" -exec cat {} \;
   ```

2. **修复生成器配置**：
   - 检查 `src/generators/bwp-java-generator.ts` 中的 toString 生成逻辑
   - 验证 `src/generators/external-api-client-generator.ts` 中的 URL 处理
   - 更新 `src/generators/properties-generator.ts` 中的配置转换

3. **验证修复效果**：
   ```bash
   # 重新生成代码
   node dist/cli.js auto test/_fixtures/ -p com.example.movies
   
   # 检查生成的配置
   cat spring-boilerplate/src/main/resources/application.properties
   
   # 测试修复结果
   cd spring-boilerplate && mvn spring-boot:run
   curl "http://localhost:8080/movies?searchString=batman"
   ```

**预期修复结果**：
- toString 方法正确生成
- 外部 API URL 配置正确（应为完整的 http://www.omdbapi.com/ URL）
- API 返回完整的电影搜索数据

请按照这个策略逐步修复问题，并确保每个修复点都经过验证。
```

## 示例 3：基于现有测试框架的验证

### 利用项目中的测试基础设施

```markdown
我需要利用现有的测试框架来验证 Tibco BW 迁移的质量。

**测试策略**：基于项目中的 `test/` 目录结构

**执行测试验证**：

1. **单元测试验证**：
   ```bash
   # 运行解析器测试
   npm test -- test/unit/bwp-parser.test.ts
   npm test -- test/unit/xsd-parser.test.ts
   
   # 运行生成器测试
   npm test -- test/unit/bwp-java-generator.test.ts
   npm test -- test/unit/java-generator.test.ts
   ```

2. **集成测试验证**：
   ```bash
   # 端到端自动化测试
   npm test -- test/integration/end-to-end-automation.test.ts
   
   # Spring Boot 部署测试
   npm test -- test/integration/spring-boot-deployment.test.ts
   
   # XSD 模型集成测试
   npm test -- test/integration/xsd-java-model.test.ts
   ```

3. **CLI 功能测试**：
   ```bash
   # CLI 集成测试
   npm test -- test/integration/cli.test.ts
   
   # BWP 集成测试
   npm test -- test/integration/bwp-integration.test.ts
   ```

**测试覆盖验证**：
- [ ] 所有单元测试通过（解析、生成、工具类）
- [ ] 集成测试通过（端到端流程）
- [ ] CLI 命令功能正常
- [ ] Spring Boot 部署成功

**质量指标**：
- 测试覆盖率 > 80%
- 所有关键路径测试通过
- 性能测试在可接受范围内

**如果测试失败**：
1. 分析失败的测试用例
2. 检查相关的源代码变更
3. 修复问题并重新运行测试
4. 更新测试用例（如果需要）

请确保所有测试都通过，并提供测试报告。
```

## 示例 4：生产就绪优化

### 基于项目架构的生产部署准备

```markdown
我需要将迁移完成的 Spring Boot 应用准备用于生产环境。

**当前状态**：
- 基本迁移完成
- 功能测试通过
- 需要生产环境优化

**优化任务**：

1. **配置管理优化**：
   ```bash
   # 检查当前配置
   cat spring-boilerplate/src/main/resources/application.properties
   
   # 创建环境特定配置
   # application-dev.properties
   # application-prod.properties
   ```

2. **安全配置**：
   - API 密钥外部化（环境变量）
   - HTTPS 配置
   - 访问控制

3. **监控和日志**：
   - 集成 Spring Boot Actuator
   - 配置日志级别和格式
   - 添加健康检查端点

4. **性能优化**：
   - 连接池配置
   - 缓存策略
   - 异步处理

**验证生产就绪性**：
```bash
# 构建生产版本
cd spring-boilerplate
mvn clean package -Pprod

# 运行生产配置
java -jar target/movies-api-1.0.0.jar --spring.profiles.active=prod

# 健康检查
curl http://localhost:8080/actuator/health

# 性能测试
ab -n 1000 -c 10 http://localhost:8080/movies?searchString=test
```

**部署文档生成**：
- 环境要求说明
- 配置参数文档
- 部署步骤指南
- 故障排除手册

请确保应用满足生产环境的所有要求。
```

## 使用这些示例的建议

### 1. 根据项目阶段选择
- **初始迁移**：使用示例 1
- **问题修复**：使用示例 2  
- **质量验证**：使用示例 3
- **生产准备**：使用示例 4

### 2. 自定义调整
- 替换具体的路径和包名
- 调整验证标准
- 添加项目特定的业务逻辑

### 3. 迭代改进
- 记录每次使用的结果
- 根据反馈优化提示词
- 建立项目特定的最佳实践

### 4. 团队协作
- 标准化提示词模板
- 共享成功案例
- 建立知识库

这些优化的提示词充分利用了您项目的现有功能和架构，能够显著提高迁移的成功率和效率。
