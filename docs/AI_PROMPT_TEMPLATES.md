# AI 提示词模板集合

## 快速开始模板

### 🚀 一键自动迁移（推荐）

```markdown
我正在使用一个成熟的 Tibco BW 转 Spring Boot 的 CLI 工具进行遗留系统迁移。

**项目路径**：`test/_fixtures/MovieApi_Final_withConsul/`
**目标包名**：`com.example.movies`

请帮我执行完整的自动化迁移流程：

1. **自动转换**：
   ```bash
   node dist/cli.js auto test/_fixtures/ -p com.example.movies --port 8080
   ```

2. **验证要求**：
   - BWP 文件解析成功
   - XSD 模型生成完整（预期 30+ 个类）
   - Controller 和 Service 代码生成
   - Spring Boot 项目编译通过
   - API 测试生成并通过

3. **如果出现问题**：
   - 分析错误日志
   - 使用单步调试命令
   - 修复配置问题

请确保所有测试通过，应用能正常启动并响应 API 请求。
```

## 分阶段迁移模板

### 📋 阶段 1：项目分析

```markdown
我需要分析一个 Tibco BW 项目的迁移可行性。

**项目路径**：`{TIBCO_BW_PROJECT_PATH}`

请帮我：

1. **结构分析**：
   ```bash
   # 验证 BWP 文件
   find {TIBCO_BW_PROJECT_PATH} -name "*.bwp" -exec node dist/cli.js validate -i {} \;
   
   # 检查项目结构
   node dist/cli.js auto {TIBCO_BW_PROJECT_PATH} --no-deploy --no-app-start
   ```

2. **生成分析报告**：
   - BWP 文件数量和复杂度
   - XSD Schema 结构
   - 外部依赖识别
   - 潜在风险评估

3. **制定迁移计划**：
   - 优先级排序
   - 分阶段策略
   - 资源需求评估

请提供详细的分析结果和建议的迁移路径。
```

### 🔧 阶段 2：核心转换

```markdown
我已完成项目分析，现在需要执行核心转换。

**当前状态**：项目分析完成，准备开始转换
**目标**：生成可编译的 Spring Boot 代码

请帮我：

1. **执行转换**：
   ```bash
   node dist/cli.js auto {TIBCO_BW_PROJECT_PATH} -p {PACKAGE_NAME} --no-app-start
   ```

2. **验证生成结果**：
   - 检查生成的 Controller 和 Service
   - 验证 XSD 模型类
   - 确认 application.properties 配置

3. **修复常见问题**：
   - toString 方法问题
   - 外部 API 配置
   - 数据类型映射

4. **编译验证**：
   ```bash
   cd spring-boilerplate
   mvn compile
   ```

请确保生成的代码结构正确且能编译通过。
```

### 🧪 阶段 3：API 测试验证

```markdown
我已完成代码转换，现在需要验证 API 功能。

**当前状态**：Spring Boot 代码生成完成，编译通过
**目标**：验证 API 功能正确性

请帮我：

1. **生成并执行 API 测试**：
   ```bash
   node dist/cli.js test-api {TIBCO_BW_PROJECT_PATH} \
     --spring-boot-project spring-boilerplate \
     -p {PACKAGE_NAME} \
     --port 8080
   ```

2. **验证检查点**：
   - [ ] Spring Boot 应用启动成功
   - [ ] API 端点响应正确
   - [ ] 与原始 Swagger 规范一致
   - [ ] 外部 API 调用正常

3. **功能测试**：
   ```bash
   # 测试主要 API
   curl "http://localhost:8080/movies?searchString=batman"
   
   # 验证响应格式
   # 预期：{"search":[...],"totalResults":"613","response":"True"}
   ```

4. **问题排查**：
   - 如果 API 返回空数据，检查外部 API 配置
   - 如果启动失败，检查依赖和配置
   - 如果测试失败，分析错误日志

请确保所有 API 功能正常工作。
```

## 问题解决模板

### 🔍 配置问题诊断

```markdown
我的 Tibco BW 迁移项目遇到配置相关问题。

**问题描述**：{具体问题描述}
**错误信息**：
```
{错误日志}
```

请帮我：

1. **诊断问题**：
   - 分析错误日志
   - 检查相关配置文件
   - 识别根本原因

2. **解决方案**：
   - 提供具体的修复步骤
   - 更新配置文件
   - 验证修复效果

3. **预防措施**：
   - 避免类似问题的最佳实践
   - 配置验证方法

请提供详细的解决方案和验证步骤。
```

### 🚨 API 调用问题修复

```markdown
我的迁移项目中外部 API 调用有问题。

**问题现象**：
- API 返回空数据或错误
- 外部服务调用失败
- 参数映射不正确

**当前配置**：
- 项目路径：{TIBCO_BW_PROJECT_PATH}
- 生成的代码路径：spring-boilerplate

请帮我：

1. **分析原始配置**：
   - 检查 .substvar 文件中的 API 配置
   - 分析 BWP 文件中的外部服务调用
   - 验证参数映射规则

2. **修复生成的代码**：
   - 更新 application.properties
   - 修正 Service 类中的 API 调用
   - 调整参数映射逻辑

3. **验证修复**：
   ```bash
   # 重新启动应用
   cd spring-boilerplate
   mvn spring-boot:run
   
   # 测试 API
   curl "http://localhost:8080/movies?searchString=batman"
   ```

请确保外部 API 调用正常工作。
```

## 高级场景模板

### 🏗️ 复杂项目迁移

```markdown
我需要迁移一个包含多个 BWP 文件和复杂依赖的大型 Tibco BW 项目。

**项目特点**：
- 多个 BWP 文件
- 复杂的 XSD Schema 依赖
- 多个外部 API 集成
- 自定义配置较多

**迁移策略**：
1. **分批迁移**：按业务模块分组
2. **依赖管理**：处理模块间依赖
3. **配置统一**：合并配置文件
4. **测试策略**：分层测试验证

请帮我制定详细的迁移计划和执行步骤。
```

### 📊 迁移质量评估

```markdown
我已完成 Tibco BW 项目迁移，需要进行质量评估。

**评估维度**：
1. **功能完整性**：所有原有功能是否正确迁移
2. **性能对比**：迁移前后的性能差异
3. **代码质量**：生成代码的可维护性
4. **部署就绪性**：生产环境部署准备

**评估方法**：
- 自动化测试覆盖率
- API 一致性验证
- 性能基准测试
- 代码质量扫描

请帮我设计评估方案并执行评估。
```

## 使用指南

### 选择合适的模板

1. **新手用户**：使用"一键自动迁移"模板
2. **复杂项目**：使用"分阶段迁移"模板
3. **遇到问题**：使用"问题解决"模板
4. **高级需求**：使用"高级场景"模板

### 自定义模板

根据您的具体需求，可以：
- 调整包名和路径
- 修改验证标准
- 添加特定的业务逻辑验证
- 集成您的 CI/CD 流程

### 最佳实践

1. **逐步执行**：不要跳过验证步骤
2. **保存日志**：记录每次执行的结果
3. **版本控制**：及时提交代码变更
4. **文档更新**：更新项目文档和配置说明
