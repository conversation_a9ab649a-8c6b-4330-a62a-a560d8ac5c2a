import {
  ParsedBWPProcess,
  ParsedRestEndpoint,
  JavaGenerationContext,
  JavaMethod,
  JavaParameter,
  JavaField,
  JavaGenerationOptions
} from '../types/index';
import { ParsedConfiguration } from '../parsers/config-parser';
import { ExternalApiClientGenerator } from './external-api-client-generator';
import { ParameterMapper } from '../utils/parameter-mapper';

/**
 * BWP 到 Java Spring Boot 代码生成器
 */
export class BWPJavaGenerator {
  private options: JavaGenerationOptions;
  private config?: ParsedConfiguration;
  private usedConfigKeys: Set<string> = new Set();

  constructor(options: JavaGenerationOptions, config?: ParsedConfiguration) {
    this.options = options;
    this.config = config;
  }

  /**
   * 获取使用的配置键
   */
  getUsedConfigKeys(): Set<string> {
    return this.usedConfigKeys;
  }

  /**
   * 生成 Spring Boot 控制器代码
   */
  generateController(process: ParsedBWPProcess): string {
    const context = this.createControllerContext(process);
    return this.generateJavaClass(context);
  }

  /**
   * 生成 Spring Boot 服务代码
   */
  generateService(process: ParsedBWPProcess): string {
    const context = this.createServiceContext(process);
    return this.generateJavaClass(context);
  }

  /**
   * 创建控制器生成上下文
   */
  private createControllerContext(process: ParsedBWPProcess): JavaGenerationContext {
    const className = `${process.name}Controller`;
    const serviceName = `${process.name}Service`;
    const serviceFieldName = this.toCamelCase(serviceName);

    const context: JavaGenerationContext = {
      packageName: this.options.packageName,
      className,
      imports: new Set([
        'org.springframework.web.bind.annotation.*',
        'org.springframework.beans.factory.annotation.Autowired',
        'org.springframework.http.ResponseEntity',
        'org.springframework.http.HttpStatus',
        'jakarta.validation.Valid',
        'jakarta.annotation.Nullable',
        'org.slf4j.Logger',
        'org.slf4j.LoggerFactory'
      ]),
      annotations: ['@RestController'],
      methods: [],
      fields: []
    };

    // 添加日志字段
    context.fields.push({
      name: 'logger',
      type: 'Logger',
      annotations: [],
      visibility: 'private static final',
      initialValue: `LoggerFactory.getLogger(${className}.class)`
    });

    // 添加服务字段
    context.fields.push({
      name: serviceFieldName,
      type: serviceName,
      annotations: ['@Autowired'],
      visibility: 'private'
    });

    // 为每个 REST 端点生成控制器方法
    for (const endpoint of process.restEndpoints) {
      const method = this.createControllerMethod(endpoint, serviceFieldName, process);
      context.methods.push(method);
    }

    // 添加必要的导入
    this.addTypeImports(context, process);

    return context;
  }

  /**
   * 创建服务生成上下文
   */
  private createServiceContext(process: ParsedBWPProcess): JavaGenerationContext {
    const className = `${process.name}Service`;

    const context: JavaGenerationContext = {
      packageName: this.options.packageName,
      className,
      imports: new Set([
        'org.springframework.stereotype.Service',
        'org.springframework.web.client.RestTemplate',
        'org.springframework.beans.factory.annotation.Autowired',
        'org.slf4j.Logger',
        'org.slf4j.LoggerFactory'
      ]),
      annotations: ['@Service'],
      methods: [],
      fields: []
    };

    // 添加 Logger 字段
    context.fields.push({
      name: 'logger',
      type: 'Logger',
      annotations: [],
      visibility: 'private',
      isStatic: true,
      isFinal: true
    });

    // 添加 RestTemplate 字段
    context.fields.push({
      name: 'restTemplate',
      type: 'RestTemplate',
      annotations: ['@Autowired'],
      visibility: 'private'
    });

    // 添加配置字段（如果有外部服务调用）
    this.addConfigurationFields(context, process);

    // 为每个 REST 端点生成服务方法
    for (const endpoint of process.restEndpoints) {
      const method = this.createServiceMethod(endpoint, process);
      context.methods.push(method);
    }

    // 添加必要的导入
    this.addTypeImports(context, process);

    return context;
  }

  /**
   * 创建控制器方法
   */
  private createControllerMethod(
    endpoint: ParsedRestEndpoint,
    serviceFieldName: string,
    process: ParsedBWPProcess
  ): JavaMethod {
    const methodName = this.toCamelCase(endpoint.operationName);
    const httpMethod = endpoint.method.toLowerCase();
    const mappingAnnotation = `@${this.capitalize(httpMethod)}Mapping("${endpoint.path}")`;

    const parameters: JavaParameter[] = [];
    const paramNames: string[] = [];

    // 添加路径参数和查询参数
    for (const param of endpoint.parameters) {
      const camelCaseName = this.toCamelCase(param.name);
      const javaType = this.mapDataTypeToJava(param.dataType);
      const paramAnnotation = param.parameterType === 'Query'
        ? `@RequestParam("${param.name}")${param.required ? '' : ' @Nullable'}`
        : `@PathVariable("${param.name}")`;

      parameters.push({
        name: camelCaseName,
        type: javaType,
        annotations: [paramAnnotation]
      });
      paramNames.push(camelCaseName);
    }

    // 添加输入类型参数
    if (process.interface.inputType && process.interface.inputType !== 'void') {
      const inputTypeName = this.capitalizeTypeName(this.extractTypeName(process.interface.inputType));

      if (httpMethod === 'post' || httpMethod === 'put') {
        // 对于 POST/PUT 请求，使用请求体
        parameters.push({
          name: 'request',
          type: inputTypeName,
          annotations: ['@RequestBody', '@Valid']
        });
        paramNames.push('request');
      }
      // 对于 GET/DELETE 请求，不添加额外的请求体参数，只使用路径和查询参数
    }

    const outputTypeName = this.capitalizeTypeName(this.extractTypeName(process.interface.outputType));
    const returnType = `ResponseEntity<${outputTypeName}>`;

    const serviceCall = `${serviceFieldName}.${methodName}(${paramNames.join(', ')})`;
    const body = `
        try {
            ${outputTypeName} result = ${serviceCall};
            return ResponseEntity.ok(result);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            logger.error("Error in ${methodName}: " + e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }`;

    return {
      name: methodName,
      returnType,
      parameters,
      annotations: [mappingAnnotation],
      body,
      visibility: 'public'
    };
  }

  /**
   * 创建服务方法
   */
  private createServiceMethod(
    endpoint: ParsedRestEndpoint,
    process: ParsedBWPProcess
  ): JavaMethod {
    const methodName = this.toCamelCase(endpoint.operationName);
    const outputTypeName = this.capitalizeTypeName(this.extractTypeName(process.interface.outputType));

    const parameters: JavaParameter[] = [];
    const paramNames: string[] = [];

    // 添加参数
    for (const param of endpoint.parameters) {
      const camelCaseName = this.toCamelCase(param.name);
      parameters.push({
        name: camelCaseName,
        type: this.mapDataTypeToJava(param.dataType),
        annotations: []
      });
      paramNames.push(camelCaseName);
    }

    // 如果有输入类型，添加请求参数（仅对 POST/PUT 请求）
    const httpMethod = endpoint.method.toLowerCase();
    if ((httpMethod === 'post' || httpMethod === 'put') &&
        process.interface.inputType && process.interface.inputType !== 'void') {
      const inputTypeName = this.capitalizeTypeName(this.extractTypeName(process.interface.inputType));
      parameters.push({
        name: 'request',
        type: inputTypeName,
        annotations: []
      });
      paramNames.push('request');
    }

    // 构建 REST 调用逻辑
    const restCallLogic = this.generateRestCallLogic(endpoint, paramNames, outputTypeName);

    const paramLog = paramNames.length > 0 ? paramNames.join(' + ", " + ') : '""';
    const body = `
        logger.info("Executing ${methodName} with parameters: {}", ${paramLog});

        try {
            ${restCallLogic}

            logger.info("Successfully completed ${methodName}");
            return result;
        } catch (Exception e) {
            logger.error("Error in ${methodName}: " + e.getMessage(), e);
            throw new RuntimeException("Service call failed", e);
        }`;

    return {
      name: methodName,
      returnType: outputTypeName,
      parameters,
      annotations: [],
      body,
      visibility: 'public'
    };
  }

  /**
   * 生成 REST 调用逻辑
   */
  private generateRestCallLogic(endpoint: ParsedRestEndpoint, paramNames: string[], outputTypeName: string): string {
    const method = endpoint.method.toUpperCase();

    // 检查是否为外部 API 调用
    if (this.isExternalApiEndpoint(endpoint) || this.shouldUseExternalApi(endpoint)) {
      return this.generateExternalApiCall(endpoint, paramNames, outputTypeName);
    }

    if (method === 'GET') {
      const queryParams = endpoint.parameters
        .filter(p => p.parameterType === 'Query')
        .map(p => `"${p.name}=" + ${this.toCamelCase(p.name)}`)
        .join(' + "&" + ');

      // 构建 URL - 使用配置的服务 URL 而不是相对路径
      const urlConstruction = this.buildUrlConstruction(endpoint, queryParams);

      return `${outputTypeName} result = restTemplate.getForObject(${urlConstruction}, ${outputTypeName}.class);`;
    } else if (method === 'POST') {
      const requestBody = paramNames.find(name => name === 'request') || 'null';
      const urlConstruction = this.buildUrlConstruction(endpoint, '');
      return `${outputTypeName} result = restTemplate.postForObject(${urlConstruction}, ${requestBody}, ${outputTypeName}.class);`;
    } else if (method === 'PUT') {
      const requestBody = paramNames.find(name => name === 'request') || 'null';
      const urlConstruction = this.buildUrlConstruction(endpoint, '');
      return `restTemplate.put(${urlConstruction}, ${requestBody});
            ${outputTypeName} result = new ${outputTypeName}(); // TODO: Handle PUT response`;
    } else if (method === 'DELETE') {
      const urlConstruction = this.buildUrlConstruction(endpoint, '');
      return `restTemplate.delete(${urlConstruction});
            ${outputTypeName} result = new ${outputTypeName}(); // TODO: Handle DELETE response`;
    }

    return `${outputTypeName} result = new ${outputTypeName}(); // TODO: Implement ${method} call`;
  }

  /**
   * 构建 URL 构造逻辑
   */
  private buildUrlConstruction(endpoint: ParsedRestEndpoint, queryParams: string): string {
    // 检查是否有外部服务配置
    if (endpoint.connector && this.config) {
      const serviceUrlVar = this.getServiceUrlVariable(endpoint.connector);
      if (serviceUrlVar) {
        if (queryParams) {
          return `${serviceUrlVar} + "${endpoint.path}?" + ${queryParams}`;
        } else {
          return `${serviceUrlVar} + "${endpoint.path}"`;
        }
      }
    }

    // 回退到相对路径（原有逻辑）
    if (queryParams) {
      return `"${endpoint.path}?" + ${queryParams}`;
    } else {
      return `"${endpoint.path}"`;
    }
  }

  /**
   * 获取服务 URL 变量名
   */
  private getServiceUrlVariable(connector: string): string | null {
    if (!this.config) return null;

    // 根据连接器名称确定服务类型
    if (connector.includes('HttpClientResource1') || connector.includes('Details') || connector.includes('SortMovies')) {
      return 'detailsServiceUrl';
    } else if (connector.includes('HttpClientResource') || connector.includes('Search')) {
      return 'searchServiceUrl';
    }

    return null;
  }

  /**
   * 添加配置字段
   */
  private addConfigurationFields(context: JavaGenerationContext, process: ParsedBWPProcess): void {
    const usedConfigs = new Set<string>();

    // 检查哪些外部服务被使用
    for (const endpoint of process.restEndpoints) {
      if (this.isExternalApiEndpoint(endpoint) || this.shouldUseExternalApi(endpoint)) {
        const configKey = this.getExternalApiBaseUrlConfig(endpoint);
        usedConfigs.add(configKey);
        this.usedConfigKeys.add(configKey); // 记录使用的配置键
      }
    }

    // 添加相应的配置字段
    context.imports.add('org.springframework.beans.factory.annotation.Value');

    // 动态处理所有配置键
    for (const configKey of usedConfigs) {
      // 跳过硬编码的 URL
      if (configKey.startsWith('"')) continue;

      // 生成变量名和配置属性名
      const variableName = this.getVariableNameFromConfigKey(configKey);
      const propertyName = this.getPropertyNameFromConfigKey(configKey);

      context.fields.push({
        name: variableName,
        type: 'String',
        annotations: [`@Value("\${${propertyName}}")`],
        visibility: 'private'
      });

      // 如果是 API URL，检查是否需要添加 API key
      if (configKey.endsWith('ApiUrl') && this.needsApiKey(configKey)) {
        const apiKeyVariableName = variableName.replace('Url', 'Key');
        const apiKeyPropertyName = propertyName.replace('.url', '.key');

        context.fields.push({
          name: apiKeyVariableName,
          type: 'String',
          annotations: [`@Value("\${${apiKeyPropertyName}}")`],
          visibility: 'private'
        });
      }
    }
  }

  /**
   * 生成 Java 类代码
   */
  private generateJavaClass(context: JavaGenerationContext): string {
    const imports = Array.from(context.imports).sort().map(imp => `import ${imp};`).join('\n');
    const annotations = context.annotations.join('\n');

    // 过滤掉静态 final logger 字段，因为我们会单独生成
    const nonLoggerFields = context.fields.filter(field =>
      !(field.name === 'logger' && field.isStatic && field.isFinal)
    );
    const fields = nonLoggerFields.map(field => this.generateField(field)).join('\n\n');
    const methods = context.methods.map(method => this.generateMethod(method)).join('\n\n');

    const loggerField = this.generateLoggerField(context);
    const fieldsSection = fields ? `\n    ${fields}` : '';
    const methodsSection = methods ? `\n    ${methods}` : '';

    return `package ${context.packageName};

${imports}

${annotations}
public class ${context.className} {${loggerField ? `\n    ${loggerField}` : ''}${fieldsSection}${methodsSection}
}`;
  }

  /**
   * 生成字段代码
   */
  private generateField(field: JavaField): string {
    const annotations = field.annotations.join('\n    ');
    const modifiers = [
      field.visibility,
      field.isStatic ? 'static' : '',
      field.isFinal ? 'final' : ''
    ].filter(Boolean).join(' ');

    const annotationPrefix = annotations ? `    ${annotations}\n` : '';
    const initialValue = field.initialValue ? ` = ${field.initialValue}` : '';

    return `${annotationPrefix}    ${modifiers} ${field.type} ${field.name}${initialValue};`;
  }

  /**
   * 生成方法代码
   */
  private generateMethod(method: JavaMethod): string {
    const annotations = method.annotations.map(ann => `    ${ann}`).join('\n');
    const parameters = method.parameters.map(param => {
      const paramAnnotations = param.annotations.join(' ');
      return `${paramAnnotations} ${param.type} ${param.name}`.trim();
    }).join(', ');

    const annotationPrefix = annotations ? `${annotations}\n` : '';
    
    return `${annotationPrefix}    ${method.visibility} ${method.returnType} ${method.name}(${parameters}) {${method.body}
    }`;
  }

  /**
   * 生成 Logger 字段
   */
  private generateLoggerField(context: JavaGenerationContext): string {
    if (context.fields.some(f => f.name === 'logger' && f.isStatic && f.isFinal)) {
      return `private static final Logger logger = LoggerFactory.getLogger(${context.className}.class);`;
    }
    return '';
  }

  /**
   * 添加类型导入
   */
  private addTypeImports(context: JavaGenerationContext, process: ParsedBWPProcess): void {
    // 暂时不添加命名空间导入，因为它们不是有效的 Java 包名
    // 在实际项目中，这些类型应该从 XSD 生成的 Java 类中导入

    // 添加常用的 Java 类型导入
    if (process.interface.inputType && process.interface.inputType !== 'void') {
      // context.imports.add(`com.example.model.${this.extractTypeName(process.interface.inputType)}`);
    }

    if (process.interface.outputType && process.interface.outputType !== 'void') {
      // context.imports.add(`com.example.model.${this.extractTypeName(process.interface.outputType)}`);
    }
  }

  /**
   * 工具方法：转换为驼峰命名
   */
  private toCamelCase(str: string): string {
    return str.replace(/_([a-z])/g, (match, letter) => letter.toUpperCase())
              .replace(/^[A-Z]/, (match) => match.toLowerCase());
  }

  /**
   * 工具方法：首字母大写
   */
  private capitalize(str: string): string {
    return str.charAt(0).toUpperCase() + str.slice(1);
  }

  /**
   * 工具方法：提取类型名称
   */
  private extractTypeName(fullType: string): string {
    const parts = fullType.split('.');
    return parts[parts.length - 1];
  }

  /**
   * 工具方法：将类型名称首字母大写
   */
  private capitalizeTypeName(typeName: string): string {
    return this.capitalize(typeName);
  }

  /**
   * 检查是否为外部 API 端点
   */
  private isExternalApiEndpoint(endpoint: ParsedRestEndpoint): boolean {
    // 检查 baseUrl 是否指向外部服务
    if (endpoint.baseUrl && !endpoint.baseUrl.includes('localhost') && !endpoint.baseUrl.includes('127.0.0.1')) {
      return true;
    }

    // 检查连接器是否为外部服务连接器
    if (endpoint.connector && endpoint.connector.includes('HttpClientResource1')) {
      return true;
    }

    // 特殊处理：检查连接器是否指向外部 API
    if (endpoint.connector && this.isExternalConnector(endpoint.connector)) {
      return true;
    }

    return false;
  }

  /**
   * 检查连接器是否指向外部 API
   */
  private isExternalConnector(connector: string): boolean {
    if (this.config?.httpClients?.has(connector)) {
      const httpClient = this.config.httpClients.get(connector)!;
      const hostBinding = httpClient.substitutionBindings.find((b: any) => b.template === 'host');
      if (hostBinding && hostBinding.propName) {
        const hostValue = this.config?.globalVariables?.get(hostBinding.propName)?.value;
        // 检查是否为外部主机（非 localhost）
        return !!(hostValue && !hostValue.includes('localhost') && !hostValue.includes('127.0.0.1'));
      }
    }

    // 基于连接器命名模式进行推断
    if (connector.includes('HttpClientResource') && this.hasExternalApiConfiguration()) {
      return true;
    }

    return false;
  }

  /**
   * 检查项目是否有外部 API 配置
   */
  private hasExternalApiConfiguration(): boolean {
    if (!this.config?.globalVariables) return false;

    // 检查是否有外部主机配置
    const hasExternalHost = Array.from(this.config.globalVariables.values())
      .some(v => v.value && !v.value.includes('localhost') && !v.value.includes('127.0.0.1') && v.value.includes('.'));

    // 检查是否有 API key 配置
    const hasApiKey = Array.from(this.config.globalVariables.keys())
      .some(key => key.toLowerCase().includes('key') || key.toLowerCase().includes('token'));

    return hasExternalHost && hasApiKey;
  }

  /**
   * 判断是否应该使用外部 API（通用智能检测）
   */
  private shouldUseExternalApi(endpoint: ParsedRestEndpoint): boolean {
    // 基于参数模式检测是否为外部 API 调用
    const hasSearchParam = endpoint.parameters.some(p =>
      this.isSearchLikeParameter(p.name.toLowerCase())
    );

    const hasIdParam = endpoint.parameters.some(p =>
      this.isIdLikeParameter(p.name.toLowerCase())
    );

    // 如果有搜索或 ID 参数，且项目有外部 API 配置，则可能是外部 API
    if ((hasSearchParam || hasIdParam) && this.hasExternalApiConfiguration()) {
      return true;
    }

    return false;
  }

  /**
   * 检查是否为搜索类参数
   */
  private isSearchLikeParameter(paramName: string): boolean {
    const searchPatterns = ['search', 'query', 'q', 'term', 'keyword', 'find'];
    return searchPatterns.some(pattern => paramName.includes(pattern));
  }

  /**
   * 检查是否为 ID 类参数
   */
  private isIdLikeParameter(paramName: string): boolean {
    const idPatterns = ['id', 'identifier', 'key', 'ref'];
    return idPatterns.some(pattern => paramName.includes(pattern)) &&
           !paramName.includes('api'); // 排除 apikey 等
  }

  /**
   * 检查是否为外部 API 端点（通用方法）
   */
  private isExternalApiEndpointByPattern(endpoint: ParsedRestEndpoint): boolean {
    // 检查 URL 模式
    if (endpoint.baseUrl) {
      const url = endpoint.baseUrl.toLowerCase();
      const externalPatterns = ['api.', '.api.', 'service.', 'data.'];
      if (externalPatterns.some(pattern => url.includes(pattern))) {
        return true;
      }
    }

    // 检查连接器模式
    return this.isExternalConnector(endpoint.connector || '');
  }

  /**
   * 生成外部 API 调用逻辑
   */
  private generateExternalApiCall(endpoint: ParsedRestEndpoint, paramNames: string[], outputTypeName: string): string {
    const method = endpoint.method.toUpperCase();

    if (method === 'GET') {
      // 使用参数映射器映射参数
      const mappedParams = ParameterMapper.mapExternalApiParameters(endpoint);
      const apiCallType = ParameterMapper.detectApiCallType(endpoint);

      // 生成参数映射代码
      const queryParamsCode = ParameterMapper.generateParameterMappingCode(
        endpoint.parameters,
        mappedParams
      );

      const baseUrlConfig = this.getExternalApiBaseUrlConfig(endpoint);

      // 如果是配置变量，需要添加相应的 @Value 注解
      const urlConstruction = this.generateUrlConstruction(baseUrlConfig, queryParamsCode);
      const valueAnnotation = this.generateValueAnnotation(baseUrlConfig);

      // 添加日志记录，显示实际调用的 URL
      const logStatement = `logger.info("Calling external API (${apiCallType}): {}", url);`;

      return `
            ${urlConstruction}
            ${logStatement}

            ${outputTypeName} result = restTemplate.getForObject(url, ${outputTypeName}.class);`;
    }

    return `// TODO: Implement ${method} method for external API`;
  }

  /**
   * 获取外部 API 基础 URL 配置引用（通用方法）
   */
  private getExternalApiBaseUrlConfig(endpoint: ParsedRestEndpoint): string {
    // 首先尝试从 baseUrl 推断配置键
    if (endpoint.baseUrl) {
      const configKey = this.inferConfigKeyFromBaseUrl(endpoint.baseUrl);
      if (configKey) {
        return configKey;
      }
    }

    // 基于连接器类型确定配置键
    if (endpoint.connector) {
      const serviceUrlKey = this.getServiceUrlKeyFromConnector(endpoint.connector);
      if (serviceUrlKey) {
        return serviceUrlKey;
      }
    }

    // 基于参数模式推断（作为后备方案）
    if (this.shouldUseExternalApi(endpoint)) {
      const hasSearchParam = endpoint.parameters.some(p =>
        this.isSearchLikeParameter(p.name.toLowerCase())
      );

      if (hasSearchParam) {
        return 'searchServiceUrl';
      } else {
        return 'detailsServiceUrl';
      }
    }

    return '"http://external-api.com"';
  }

  /**
   * 从 baseUrl 推断配置键（通用方法）
   */
  private inferConfigKeyFromBaseUrl(baseUrl: string): string | null {
    if (!baseUrl) return null;

    const url = baseUrl.toLowerCase();

    // 提取域名部分
    const domainMatch = url.match(/https?:\/\/([^\/]+)/);
    if (!domainMatch) return null;

    const domain = domainMatch[1];

    // 移除端口号
    const domainWithoutPort = domain.split(':')[0];

    // 将域名转换为配置键
    // 例如：www.omdbapi.com -> omdbApiUrl
    //      api.example.com -> exampleApiUrl
    //      localhost -> externalApiUrl

    if (domainWithoutPort === 'localhost' || domainWithoutPort === '127.0.0.1') {
      return 'externalApiUrl';
    }

    // 处理常见的 API 域名模式
    const parts = domainWithoutPort.split('.');
    let serviceName = '';

    if (parts.length >= 2) {
      // 查找主要的服务名称部分
      for (const part of parts) {
        if (part !== 'www' && part !== 'api' && part !== 'com' && part !== 'org' && part !== 'net') {
          serviceName = part;
          break;
        }
      }
    }

    if (serviceName) {
      // 将服务名转换为驼峰命名并添加 ApiUrl 后缀
      const camelCaseName = serviceName.charAt(0).toLowerCase() + serviceName.slice(1);
      return `${camelCaseName}ApiUrl`;
    }

    return 'externalApiUrl';
  }

  /**
   * 从配置键生成变量名
   */
  private getVariableNameFromConfigKey(configKey: string): string {
    // 直接使用配置键作为变量名（已经是驼峰命名）
    return configKey;
  }

  /**
   * 从配置键生成属性名
   */
  private getPropertyNameFromConfigKey(configKey: string): string {
    // 将驼峰命名转换为点分隔的属性名
    // 例如：omdbApiUrl -> omdb.api.url
    return configKey
      .replace(/([A-Z])/g, '.$1')
      .toLowerCase()
      .replace(/^\./, '');
  }

  /**
   * 检查是否需要 API key
   */
  private needsApiKey(configKey: string): boolean {
    // 大多数外部 API 都需要 API key
    // 可以根据具体需求扩展这个逻辑
    return configKey.endsWith('ApiUrl');
  }

  /**
   * 从连接器名称推断服务 URL 配置键
   */
  private getServiceUrlKeyFromConnector(connector: string): string | null {
    // 基于连接器命名模式推断配置键
    if (connector.includes('Search') || connector.includes('search')) {
      return 'searchServiceUrl';
    }

    if (connector.includes('Details') || connector.includes('details') || connector.includes('1')) {
      return 'detailsServiceUrl';
    }

    // 通用外部服务
    if (connector.includes('HttpClientResource')) {
      return 'externalServiceUrl';
    }

    return null;
  }

  /**
   * 生成 URL 构造代码
   */
  private generateUrlConstruction(baseUrlConfig: string, queryParamsCode: string): string {
    if (baseUrlConfig.startsWith('"') && baseUrlConfig.endsWith('"')) {
      // 硬编码 URL
      return queryParamsCode
        ? `String url = ${baseUrlConfig} + "?" + ${queryParamsCode};`
        : `String url = ${baseUrlConfig};`;
    } else {
      // 配置变量 - 直接使用现有的变量名
      const variableName = this.toCamelCase(baseUrlConfig.replace(/\./g, ''));
      return queryParamsCode
        ? `String url = ${variableName} + "?" + ${queryParamsCode};`
        : `String url = ${variableName};`;
    }
  }

  /**
   * 生成 @Value 注解
   */
  private generateValueAnnotation(baseUrlConfig: string): string {
    if (baseUrlConfig.startsWith('"') && baseUrlConfig.endsWith('"')) {
      return ''; // 硬编码 URL 不需要注解
    } else {
      return `@Value("\${${baseUrlConfig}}")`;
    }
  }

  /**
   * 从配置键获取变量名
   */
  private getVariableNameFromConfig(configKey: string): string {
    // 将配置键转换为驼峰命名的变量名
    const baseName = configKey.replace(/\./g, '').replace(/([A-Z])/g, (match, letter, index) =>
      index === 0 ? letter.toLowerCase() : letter
    );

    // 如果已经以 Url 结尾，不要重复添加
    return baseName.endsWith('Url') ? baseName : baseName + 'Url';
  }

  /**
   * 获取外部 API 基础 URL
   */
  private getExternalApiBaseUrl(endpoint: ParsedRestEndpoint): string {
    // 优先使用端点配置的 baseUrl
    if (endpoint.baseUrl && endpoint.baseUrl !== 'http://localhost:7777/') {
      return endpoint.baseUrl.replace(/\/$/, ''); // 移除末尾的斜杠
    }

    // 从连接器配置中获取 URL
    if (endpoint.connector && this.config?.httpClients?.has(endpoint.connector)) {
      const httpClient = this.config.httpClients.get(endpoint.connector)!;
      if (httpClient.host) {
        // 从替换绑定中获取端口
        const portBinding = httpClient.substitutionBindings.find(b => b.template === 'port');
        const port = portBinding ? this.config?.globalVariables?.get(portBinding.propName)?.value : undefined;

        // 确定协议
        const protocol = this.determineProtocol(httpClient.host, port);
        const portSuffix = port && port !== '80' && port !== '443' ? `:${port}` : '';

        return `${protocol}://${httpClient.host}${portSuffix}`;
      }
    }

    // 从服务 URL 映射中获取
    if (this.config?.serviceUrls) {
      const serviceUrls = Array.from(this.config.serviceUrls.entries());
      for (const [key, url] of serviceUrls) {
        if (key.includes('url') && !url.includes('localhost')) {
          return url;
        }
      }
    }

    return 'http://external-api.com';
  }

  /**
   * 确定协议
   */
  private determineProtocol(host: string, port?: string): string {
    if (port === '443') return 'https';
    if (port === '80') return 'http';

    // 基于主机名确定协议
    if (host.includes('localhost') || host.includes('127.0.0.1')) {
      return 'http';
    }

    // 特殊情况：OMDB API 使用 HTTP
    if (host.includes('omdbapi.com')) {
      return 'http';
    }

    // 默认使用 HTTPS
    return 'https';
  }

  /**
   * 工具方法：映射数据类型到 Java 类型
   */
  private mapDataTypeToJava(dataType: string): string {
    const typeMap: { [key: string]: string } = {
      'string': 'String',
      'int': 'Integer',
      'integer': 'Integer',
      'long': 'Long',
      'double': 'Double',
      'float': 'Float',
      'boolean': 'Boolean',
      'date': 'LocalDate',
      'dateTime': 'LocalDateTime'
    };

    return typeMap[dataType.toLowerCase()] || 'String';
  }
}
