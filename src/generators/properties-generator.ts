import { ParsedConfiguration } from '../parsers/config-parser';

/**
 * Spring Boot application.properties 生成器
 */
export class PropertiesGenerator {
  
  /**
   * 生成 application.properties 内容
   */
  generateApplicationProperties(config: ParsedConfiguration, appName: string = 'movies-api', requiredConfigs?: Set<string>): string {
    const lines: string[] = [];

    // 基本应用配置
    lines.push(`spring.application.name=${appName}`);
    lines.push('');
    lines.push('# Server Configuration');
    lines.push('server.port=8080');
    lines.push('server.servlet.context-path=/');
    lines.push('');

    // 外部服务配置
    this.addServiceConfigurations(lines, config, requiredConfigs);

    // 日志配置
    lines.push('# Logging Configuration');
    lines.push('logging.level.com.example.movies=DEBUG');
    lines.push('logging.level.org.springframework.web=INFO');
    lines.push('');

    // Actuator 配置
    lines.push('# Actuator Configuration');
    lines.push('management.endpoints.web.exposure.include=health,info,metrics');
    lines.push('management.endpoint.health.show-details=when-authorized');

    return lines.join('\n');
  }

  /**
   * 添加服务配置
   */
  private addServiceConfigurations(lines: string[], config: ParsedConfiguration, requiredConfigs?: Set<string>): void {
    const detailsHost = config.globalVariables.get('DetailsServiceHost')?.value;
    const detailsPort = config.globalVariables.get('DetailsServicePort')?.value;
    const searchHost = config.globalVariables.get('SearchServiceHost')?.value;
    const searchPort = config.globalVariables.get('SearchServicePort')?.value;
    const apiKey = config.globalVariables.get('apikey')?.value;

    // OMDB API 配置
    if (detailsHost === 'www.omdbapi.com') {
      lines.push('# OMDB API Configuration');
      lines.push('omdb.api.url=http://www.omdbapi.com/');
      if (apiKey) {
        lines.push(`omdb.api.key=${apiKey}`);
      } else {
        lines.push('omdb.api.key=');
      }
      lines.push('');

      // Details Service 配置（指向 OMDB）
      lines.push('# Details Service Configuration (OMDB API)');
      lines.push(`details.service.host=${detailsHost}`);
      if (detailsPort) {
        lines.push(`details.service.port=${detailsPort}`);
        const protocol = detailsPort === '443' ? 'https' : 'http';
        lines.push(`details.service.url=${protocol}://\${details.service.host}`);
      } else {
        lines.push('details.service.port=80');
        lines.push('details.service.url=http://${details.service.host}');
      }
      lines.push('');
    } else if (detailsHost) {
      // 其他外部详情服务
      lines.push('# Details Service Configuration');
      lines.push(`details.service.host=${detailsHost}`);
      if (detailsPort) {
        lines.push(`details.service.port=${detailsPort}`);
        const protocol = detailsPort === '443' ? 'https' : 'http';
        lines.push(`details.service.url=${protocol}://\${details.service.host}:\${details.service.port}`);
      } else {
        lines.push('details.service.port=80');
        lines.push('details.service.url=http://${details.service.host}:${details.service.port}');
      }
      lines.push('');
    }

    // Search Service 配置
    if (searchHost) {
      lines.push('# Search Service Configuration');
      lines.push(`search.service.host=${searchHost}`);
      if (searchPort) {
        lines.push(`search.service.port=${searchPort}`);
        const protocol = searchPort === '443' ? 'https' : 'http';
        lines.push(`search.service.url=${protocol}://\${search.service.host}:\${search.service.port}`);
      } else {
        lines.push('search.service.port=8080');
        lines.push('search.service.url=http://${search.service.host}:${search.service.port}');
      }
      lines.push('');
    }

    // 添加动态配置（基于 requiredConfigs）
    if (requiredConfigs && requiredConfigs.size > 0) {
      this.addDynamicConfigurations(lines, config, requiredConfigs);
    }

    // 其他全局变量
    const otherVars = Array.from(config.globalVariables.entries())
      .filter(([name]) => !['DetailsServiceHost', 'DetailsServicePort', 'SearchServiceHost', 'SearchServicePort', 'apikey'].includes(name));

    if (otherVars.length > 0) {
      lines.push('# Other Configuration');
      for (const [name, variable] of otherVars) {
        lines.push(`${this.convertToPropertyName(name)}=${variable.value}`);
      }
      lines.push('');
    }
  }

  /**
   * 添加动态配置
   */
  private addDynamicConfigurations(lines: string[], config: ParsedConfiguration, requiredConfigs: Set<string>): void {
    const processedConfigs = new Set<string>();

    for (const configKey of requiredConfigs) {
      // 跳过硬编码的 URL
      if (configKey.startsWith('"')) continue;

      // 跳过已经处理过的配置
      if (processedConfigs.has(configKey)) continue;

      const propertyName = this.convertConfigKeyToPropertyName(configKey);
      const serviceName = this.extractServiceNameFromConfigKey(configKey);

      // 添加服务配置组
      lines.push(`# ${serviceName} Configuration`);

      if (configKey.endsWith('ApiUrl')) {
        // API URL 配置
        const basePropertyName = propertyName.replace('.url', '');
        lines.push(`${basePropertyName}.url=http://external-api.com/`);
        lines.push(`${basePropertyName}.key=your-api-key-here`);
      } else {
        // 通用配置
        lines.push(`${propertyName}=http://external-service.com/`);
      }

      lines.push('');
      processedConfigs.add(configKey);
    }
  }

  /**
   * 从配置键提取服务名称
   */
  private extractServiceNameFromConfigKey(configKey: string): string {
    // 例如：omdbApiUrl -> OMDB API
    //      externalApiUrl -> External API
    const serviceName = configKey
      .replace(/ApiUrl$/, '')
      .replace(/ServiceUrl$/, '')
      .replace(/([A-Z])/g, ' $1')
      .trim();

    return serviceName.charAt(0).toUpperCase() + serviceName.slice(1) + ' API';
  }

  /**
   * 将配置键转换为属性名
   */
  private convertConfigKeyToPropertyName(configKey: string): string {
    // 例如：omdbApiUrl -> omdb.api.url
    return configKey
      .replace(/([A-Z])/g, '.$1')
      .toLowerCase()
      .replace(/^\./, '');
  }

  /**
   * 转换变量名为属性名格式
   */
  private convertToPropertyName(variableName: string): string {
    return variableName
      .replace(/([A-Z])/g, '.$1')
      .toLowerCase()
      .replace(/^\./, '');
  }
}
